{"name": "file-extension", "version": "4.0.5", "description": "Get the extension of a given filename or path", "author": "silverwind <<EMAIL>> (https://github.com/silverwind)", "repository": "silverwind/file-extension", "license": "BSD-2-<PERSON><PERSON>", "main": "file-extension.js", "scripts": {"test": "make test"}, "engines": {"node": ">=4"}, "files": ["file-extension.js", "file-extension.min.js"], "keywords": ["file", "extension", "extname", "ext", "file extension", "file-extension"], "devDependencies": {"eslint": "^4.19.1", "eslint-config-silverwind": "^1.0.42", "gzip-size": "^4.1.0", "gzip-size-cli": "^2.1.0", "semver": "^5.5.0", "uglify-js": "^3.3.28", "updates": "^3.0.0"}}
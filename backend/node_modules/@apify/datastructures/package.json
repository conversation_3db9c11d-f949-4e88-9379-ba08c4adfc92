{"name": "@apify/datastructures", "version": "2.0.3", "description": "Tools and constants shared across Apify projects.", "main": "./cjs/index.cjs", "module": "./esm/index.mjs", "typings": "./cjs/index.d.ts", "exports": {".": {"import": {"types": "./esm/index.d.mts", "default": "./esm/index.mjs"}, "require": {"types": "./cjs/index.d.ts", "default": "./cjs/index.cjs"}}}, "keywords": ["apify"], "author": {"name": "Apify", "email": "<EMAIL>", "url": "https://apify.com"}, "contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"], "license": "Apache-2.0", "repository": {"type": "git", "url": "git+https://github.com/apify/apify-shared-js"}, "bugs": {"url": "https://github.com/apify/apify-shared-js/issues"}, "homepage": "https://apify.com", "scripts": {"build": "npm run clean && npm run compile && npm run copy", "clean": "rimraf ./dist", "compile": "tsup", "copy": "ts-node -T ../../scripts/copy.ts"}, "publishConfig": {"access": "public"}, "gitHead": "eed90a991dd02c9a9be607fa38d178c6d6e7ff35"}
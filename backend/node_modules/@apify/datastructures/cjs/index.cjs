"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);

// src/index.ts
var index_exports = {};
__export(index_exports, {
  LinkedList: () => LinkedList,
  LinkedListNode: () => LinkedListNode,
  ListDictionary: () => ListDictionary,
  LruCache: () => LruCache
});
module.exports = __toCommonJS(index_exports);

// src/linked_list.ts
var dataEqual = /* @__PURE__ */ __name((data1, data2) => {
  if (data1 === null) return data2 === null;
  if (data1.equals) return data1.equals(data2);
  return data1 === data2;
}, "dataEqual");
var _LinkedListNode = class _LinkedListNode {
  constructor(data) {
    this.data = data;
    __publicField(this, "prev", null);
    __publicField(this, "next", null);
    __publicField(this, "dictKey");
  }
};
__name(_LinkedListNode, "LinkedListNode");
var LinkedListNode = _LinkedListNode;
var _LinkedList = class _LinkedList {
  constructor() {
    __publicField(this, "head", null);
    __publicField(this, "tail", null);
    __publicField(this, "length", 0);
  }
  /**
    * Appends a new node with specific data to the end of the linked list.
    */
  add(data, toFirstPosition) {
    const node = new LinkedListNode(data);
    this.addNode(node, toFirstPosition);
    return node;
  }
  /**
   * Appends a new node to the end of the linked list or the beginning if firstPosition is true-ish.
   */
  addNode(node, toFirstPosition) {
    if (typeof node !== "object" || node === null) throw new Error('Parameter "node" must be an object');
    if (node.prev || node.next) throw new Error("New node is still included in some linked list");
    node.prev = null;
    node.next = null;
    if (this.length === 0) {
      this.tail = node;
      this.head = node;
    } else if (toFirstPosition) {
      node.next = this.head;
      this.head.prev = node;
      this.head = node;
    } else {
      node.prev = this.tail;
      this.tail.next = node;
      this.tail = node;
    }
    this.length++;
  }
  /**
   * Finds a first node that holds a specific data object. See 'dataEqual' function for a description
   * how the object equality is tested. Function returns null if the data cannot be found.
   */
  find(data) {
    for (let node = this.head; node != null; node = node.next) {
      if (dataEqual(node.data, data)) {
        return node;
      }
    }
    return null;
  }
  removeNode(node) {
    if (typeof node !== "object" || node == null) throw new Error('Parameter "node" must be an object');
    if (node.prev != null) {
      if (node.next != null) {
        node.prev.next = node.next;
        node.next.prev = node.prev;
        node.prev = null;
        node.next = null;
      } else {
        this.tail = node.prev;
        node.prev.next = null;
        node.prev = null;
      }
    } else if (node.next != null) {
      this.head = node.next;
      node.next.prev = null;
      node.next = null;
    } else {
      this.head = null;
      this.tail = null;
      node.next = null;
      node.prev = null;
    }
    this.length--;
  }
  /**
   * Removes the first item from the list. The function
   * returns the item object or null if the list is empty.
   */
  removeFirst() {
    const { head } = this;
    if (!head) return null;
    this.removeNode(head);
    return head.data;
  }
};
__name(_LinkedList, "LinkedList");
var LinkedList = _LinkedList;

// src/list_dictionary.ts
var _ListDictionary = class _ListDictionary {
  constructor() {
    __publicField(this, "linkedList", new LinkedList());
    __publicField(this, "dictionary", {});
  }
  /**
   * Gets the number of item in the list.
   */
  length() {
    return this.linkedList.length;
  }
  /**
   * Adds an item to the list. If there is already an item with same key, the function
   * returns false and doesn't make any changes. Otherwise, it returns true.
   */
  add(key, item, toFirstPosition) {
    if (typeof key !== "string") throw new Error('Parameter "key" must be a string.');
    if (key in this.dictionary) return false;
    const linkedListNode = this.linkedList.add(item, toFirstPosition);
    linkedListNode.dictKey = key;
    this.dictionary[key] = linkedListNode;
    return true;
  }
  /**
   * Gets the first item in the list. The function returns null if the list is empty.
   */
  getFirst() {
    const { head } = this.linkedList;
    if (head) return head.data;
    return null;
  }
  /**
   * Gets the last item in the list. The function returns null if the list is empty.
   */
  getLast() {
    const { tail } = this.linkedList;
    if (tail) return tail.data;
    return null;
  }
  /**
   * Gets the first item from the list and moves it to the end of the list.
   * The function returns null if the queue is empty.
   */
  moveFirstToEnd() {
    const node = this.linkedList.head;
    if (!node) return null;
    this.linkedList.removeNode(node);
    this.linkedList.addNode(node);
    return node.data;
  }
  /**
   * Removes the first item from the list.
   * The function returns the item or null if the list is empty.
   */
  removeFirst() {
    const { head } = this.linkedList;
    if (!head) return null;
    this.linkedList.removeNode(head);
    delete this.dictionary[head.dictKey];
    return head.data;
  }
  /**
   * Removes the last item from the list.
   * The function returns the item or null if the list is empty.
   */
  removeLast() {
    const { tail } = this.linkedList;
    if (!tail) return null;
    this.linkedList.removeNode(tail);
    delete this.dictionary[tail.dictKey];
    return tail.data;
  }
  /**
   * Removes an item identified by a key. The function returns the
   * object if it was found or null if it wasn't.
   */
  remove(key) {
    if (typeof key !== "string") throw new Error('Parameter "key" must be a string.');
    const node = this.dictionary[key];
    if (!node) return null;
    delete this.dictionary[key];
    this.linkedList.removeNode(node);
    return node.data;
  }
  /**
   * Finds a request based on the URL.
   */
  get(key) {
    if (typeof key !== "string") throw new Error('Parameter "key" must be a string.');
    const node = this.dictionary[key];
    if (!node) return null;
    return node.data;
  }
  /**
   * Removes all items from the list.
   */
  clear() {
    if (this.linkedList.length > 0) {
      this.linkedList = new LinkedList();
      this.dictionary = {};
    }
  }
};
__name(_ListDictionary, "ListDictionary");
var ListDictionary = _ListDictionary;

// src/lru_cache.ts
var _LruCache = class _LruCache {
  constructor(options) {
    this.options = options;
    __publicField(this, "listDictionary", new ListDictionary());
    __publicField(this, "maxLength");
    if (typeof options.maxLength !== "number") {
      throw new Error('Parameter "maxLength" must be a number.');
    }
    this.maxLength = options.maxLength;
  }
  /**
   * Gets the number of item in the list.
   */
  length() {
    return this.listDictionary.length();
  }
  /**
   * Get item from Cache and move to last position
   */
  get(key) {
    if (typeof key !== "string") throw new Error('Parameter "key" must be a string.');
    const node = this.listDictionary.dictionary[key];
    if (!node) return null;
    this.listDictionary.remove(key);
    this.listDictionary.add(key, node.data);
    return node.data;
  }
  /**
   * Add new item to cache, remove least used item if length exceeds maxLength
   */
  add(key, value) {
    const added = this.listDictionary.add(key, value);
    if (!added) return false;
    if (this.length() > this.maxLength) {
      this.listDictionary.removeFirst();
    }
    return true;
  }
  /**
   * Remove item with key
   */
  remove(key) {
    return this.listDictionary.remove(key);
  }
  /**
   * Clear cache
   */
  clear() {
    return this.listDictionary.clear();
  }
};
__name(_LruCache, "LruCache");
var LruCache = _LruCache;
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  LinkedList,
  LinkedListNode,
  ListDictionary,
  LruCache
});
/*!
 * This module defines the LinkedList class, which represents a doubly-linked list data structure.
 *
 * Author: Jan Curn (<EMAIL>)
 * Copyright(c) 2014 Apify. All rights reserved.
 *
 */
/*!
 * This module defines the ListDictionary class, a data structure
 * that combines a linked list and a dictionary.
 *
 * Author: Jan Curn (<EMAIL>)
 * Copyright(c) 2015 Apify. All rights reserved.
 *
 */
//# sourceMappingURL=index.cjs.map
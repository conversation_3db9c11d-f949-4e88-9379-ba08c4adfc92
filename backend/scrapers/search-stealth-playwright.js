const express = require('express');
const { chromium } = require('playwright');
const router = express.Router();

// Helper function to extract links from a page
async function extractLinksFromPage(url, options = {}) {
  const {
    maxLinks = 20,
    linkPattern = null,
    timeout = 60000
  } = options;

  let browser;
  let context;
  let page;

  try {
    console.log(`Starting link extraction from: ${url}`);

    // Launch browser with stealth settings
    browser = await chromium.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
      ]
    });

    // Create context with stealth settings
    context = await browser.newContext({
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      viewport: { width: 1920, height: 1080 },
      locale: 'en-US',
      timezoneId: 'America/New_York',
      extraHTTPHeaders: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      }
    });

    page = await context.newPage();

    // Set additional stealth measures
    await page.addInitScript(() => {
      // Remove webdriver property
      delete navigator.__proto__.webdriver;
      
      // Mock plugins
      Object.defineProperty(navigator, 'plugins', {
        get: () => [1, 2, 3, 4, 5]
      });
      
      // Mock languages
      Object.defineProperty(navigator, 'languages', {
        get: () => ['en-US', 'en']
      });
    });

    // Navigate to the page
    console.log('Navigating to page...');
    await page.goto(url, { 
      waitUntil: 'networkidle',
      timeout: timeout 
    });

    // Wait for content to load
    await page.waitForTimeout(3000);

    // Extract all links from the page
    console.log('Extracting links...');
    const links = await page.evaluate(({ maxLinks, linkPattern }) => {
      const allLinks = Array.from(document.querySelectorAll('a[href]'));
      const extractedLinks = [];
      const seenUrls = new Set();

      for (const link of allLinks) {
        if (extractedLinks.length >= maxLinks) break;

        let href = link.href;
        
        // Skip invalid links
        if (!href || href.startsWith('javascript:') || href.startsWith('mailto:') || href.startsWith('tel:')) {
          continue;
        }

        // Convert relative URLs to absolute
        try {
          const url = new URL(href, window.location.href);
          href = url.href;
        } catch (e) {
          continue;
        }

        // Apply pattern filter if provided
        if (linkPattern && !href.includes(linkPattern)) {
          continue;
        }

        // Skip duplicates
        if (seenUrls.has(href)) {
          continue;
        }

        seenUrls.add(href);

        // Extract link information
        const linkText = link.textContent?.trim() || '';
        const title = link.title || '';
        const imageElement = link.querySelector('img');
        const image = imageElement ? imageElement.src : null;

        extractedLinks.push({
          url: href,
          text: linkText,
          title: title,
          image: image,
          domain: new URL(href).hostname
        });
      }

      return extractedLinks;
    }, { maxLinks, linkPattern });

    console.log(`Extracted ${links.length} links`);

    // Get page metadata
    const pageInfo = await page.evaluate(() => {
      return {
        title: document.title,
        url: window.location.href,
        description: document.querySelector('meta[name="description"]')?.content || '',
        totalLinks: document.querySelectorAll('a[href]').length
      };
    });

    return {
      success: true,
      data: {
        pageInfo,
        links,
        extractedCount: links.length,
        totalLinksOnPage: pageInfo.totalLinks,
        extractedAt: new Date().toISOString()
      }
    };

  } catch (error) {
    console.error('Link extraction error:', error);
    return {
      success: false,
      error: 'Failed to extract links',
      message: error.message
    };
  } finally {
    try {
      if (page) await page.close();
      if (context) await context.close();
      if (browser) await browser.close();
    } catch (e) {
      console.error('Error closing browser:', e);
    }
  }
}

// POST endpoint for link extraction
router.post('/', async (req, res) => {
  try {
    const { url, maxLinks = 20, linkPattern } = req.body;

    if (!url) {
      return res.status(400).json({
        success: false,
        error: 'URL is required'
      });
    }

    // Validate URL
    try {
      new URL(url);
    } catch (e) {
      return res.status(400).json({
        success: false,
        error: 'Invalid URL format'
      });
    }

    // Validate maxLinks
    const maxLinksNum = parseInt(maxLinks);
    if (isNaN(maxLinksNum) || maxLinksNum < 1 || maxLinksNum > 100) {
      return res.status(400).json({
        success: false,
        error: 'maxLinks must be a number between 1 and 100'
      });
    }

    console.log(`Link extraction request: ${url} (max: ${maxLinksNum})`);

    const result = await extractLinksFromPage(url, {
      maxLinks: maxLinksNum,
      linkPattern: linkPattern?.trim() || null,
      timeout: 60000
    });

    res.json(result);

  } catch (error) {
    console.error('Route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error.message
    });
  }
});

module.exports = router;

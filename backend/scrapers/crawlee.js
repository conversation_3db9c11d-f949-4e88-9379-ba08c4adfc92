const express = require('express');
const { PlaywrightCrawler } = require('crawlee');
const { chromium } = require('playwright');

const router = express.Router();

router.post('/', async (req, res) => {
  let crawler = null;

  try {
    const { url, elements, waitTime = 5000 } = req.body;

    if (!url) {
      return res.status(400).json({ error: 'URL is required' });
    }

    console.log(`🚀 Starting Crawlee scraping for: ${url}`);
    let scrapedData = {};

    crawler = new PlaywrightCrawler({
      launchContext: {
        launchOptions: {
          headless: true,
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage'
          ]
        },
      },
      maxRequestsPerCrawl: 1,
      maxConcurrency: 1,
      requestHandlerTimeoutSecs: 60,
      async requestHandler({ page, request }) {
        try {
          console.log(`📡 Loading page: ${request.url}`);

          // Wait for the page to load
          await page.waitForLoadState('domcontentloaded');
          await page.waitForTimeout(waitTime);

          // Extract data based on requested elements
          const data = await page.evaluate((requestedElements) => {
            const result = {};

            // Helper function to get text content safely
            const getTextContent = (selector) => {
              const element = document.querySelector(selector);
              return element ? element.textContent.trim() : null;
            };

            // Helper function to get attribute safely
            const getAttribute = (selector, attribute) => {
              const element = document.querySelector(selector);
              return element ? element.getAttribute(attribute) : null;
            };

            // Helper function to get all matching elements
            const getAllElements = (selector, attribute = null) => {
              const elements = document.querySelectorAll(selector);
              return Array.from(elements).map(el => 
                attribute ? el.getAttribute(attribute) : el.textContent.trim()
              ).filter(item => item);
            };

            // Only extract elements that are specifically requested
            if (requestedElements && requestedElements.includes('title')) {
              result.title = getTextContent('title') ||
                           getTextContent('h1') ||
                           getTextContent('[data-testid="title"]') ||
                           getTextContent('.title');
            }

            // Extract description
            if (requestedElements && requestedElements.includes('description')) {
              result.description = getAttribute('meta[name="description"]', 'content') ||
                                 getAttribute('meta[property="og:description"]', 'content') ||
                                 getTextContent('.description') ||
                                 getTextContent('[data-testid="description"]');
            }

            // Extract images
            if (requestedElements && (requestedElements.includes('images') || requestedElements.includes('image'))) {
              const images = [];

              // Get og:image
              const ogImage = getAttribute('meta[property="og:image"]', 'content');
              if (ogImage) images.push(ogImage);

              // Enhanced image selectors for e-commerce sites
              const imageSelectors = [
                '.product-image img', '.item-image img', '.gallery img',
                '[data-testid*="image"] img', '.product-photo img',
                '.product-gallery img', '.main-image img', '.hero-image img',
                '.slider img', '.carousel img', '.thumbnail img',
                '[class*="image"] img', '[class*="photo"] img'
              ];

              // Get product images with specific selectors
              for (const selector of imageSelectors) {
                const imgs = getAllElements(selector, 'src');
                images.push(...imgs);
              }

              // Get all img src attributes with filtering
              const allImages = getAllElements('img', 'src');
              images.push(...allImages.filter(src => {
                if (!src || src.length < 10) return false;
                if (src.startsWith('data:')) return false;

                // Exclude common non-product images
                const excludePatterns = ['icon', 'logo', 'banner', 'ad', 'pixel', 'tracking'];
                const srcLower = src.toLowerCase();
                return !excludePatterns.some(pattern => srcLower.includes(pattern));
              }));

              result.images = [...new Set(images)]
                .filter(src => src && !src.includes('data:') && src.length > 10)
                .slice(0, 10);
            }

            // Enhanced price extraction with intelligent parsing
            if (requestedElements && requestedElements.includes('price')) {
              const priceSelectors = [
                '.price', '[data-testid="price"]', '.cost', '.amount',
                '[class*="price"]', '[id*="price"]', '.currency'
              ];

              for (const selector of priceSelectors) {
                const price = getTextContent(selector);
                if (price && /[\d.,]+/.test(price)) {
                  // Parse multiple prices from the same element
                  const prices = price.match(/[\d.,]+\s*€/g);
                  if (prices && prices.length >= 2) {
                    // First price is usually the current price, second is old price
                    result.price = prices[0].trim();
                    result.old_price = prices[1].trim();
                  } else {
                    result.price = price;
                  }
                  break;
                }
              }
            }

            // Extract old prices (only if not already found in price parsing)
            if (requestedElements && requestedElements.includes('old_price') && !result.old_price) {
              const oldPriceSelectors = [
                '.old-price', '.original-price', '.was-price', '.regular-price',
                '.price-old', '.price-before', '.crossed-price', '.strikethrough-price',
                '[data-testid="old-price"]', '[class*="old-price"]', '[class*="original-price"]',
                '[class*="was-price"]', 'del', 's', '.line-through', '.price-strike'
              ];

              for (const selector of oldPriceSelectors) {
                const oldPrice = getTextContent(selector);
                if (oldPrice && /[\d.,]+/.test(oldPrice)) {
                  result.old_price = oldPrice;
                  break;
                }
              }
            }

            // Extract author
            if (requestedElements && requestedElements.includes('author')) {
              result.author = getAttribute('meta[name="author"]', 'content') ||
                            getTextContent('.author') ||
                            getTextContent('[data-testid="author"]') ||
                            getTextContent('.byline');
            }

            return result;
          }, elements);

          scrapedData = data;

        } catch (error) {
          console.error('Page evaluation error:', error);
          throw error;
        }
      },
    });

    // Add the URL to the crawler queue
    await crawler.addRequests([url]);

    // Run the crawler
    console.log('🔄 Running crawler...');
    await crawler.run();
    console.log('✅ Crawlee scraping completed');

    const result = {
      url: url,
      scraper: 'crawlee',
      timestamp: new Date().toISOString(),
      data: scrapedData,
      success: true
    };

    // Send response first, then cleanup asynchronously
    res.json(result);

    // Cleanup asynchronously to prevent connection issues
    setTimeout(async () => {
      if (crawler) {
        try {
          // Try graceful teardown first
          await Promise.race([
            crawler.teardown(),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Teardown timeout')), 5000))
          ]);
          console.log('✅ Crawler teardown completed asynchronously');
        } catch (teardownError) {
          console.log('⚠️ Error during async crawler teardown:', teardownError.message);
          // Force cleanup if graceful teardown fails
          try {
            process.nextTick(() => {
              if (crawler && crawler.browserPool) {
                crawler.browserPool.destroy().catch(() => {});
              }
            });
          } catch (forceError) {
            console.log('⚠️ Force cleanup also failed:', forceError.message);
          }
        }
      }
    }, 100); // Small delay to ensure response is sent

  } catch (error) {
    console.error('❌ Crawlee error:', error);
    res.status(500).json({
      error: 'Failed to scrape URL with Crawlee',
      message: error.message,
      success: false
    });
  } finally {
    // Only cleanup in finally if there was an error (success case cleans up asynchronously)
    if (crawler && !res.headersSent) {
      try {
        // Quick cleanup for error cases
        setTimeout(async () => {
          try {
            await Promise.race([
              crawler.teardown(),
              new Promise((_, reject) => setTimeout(() => reject(new Error('Teardown timeout')), 3000))
            ]);
            console.log('✅ Crawler teardown completed in finally block');
          } catch (teardownError) {
            console.log('⚠️ Error during crawler teardown in finally:', teardownError.message);
          }
        }, 50);
      } catch (error) {
        console.log('⚠️ Error setting up finally cleanup:', error.message);
      }
    }
  }
});

module.exports = router;

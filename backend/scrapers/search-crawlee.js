const express = require('express');
const { PlaywrightCrawler } = require('crawlee');
const router = express.Router();

// Helper function to extract links using Crawlee
async function extractLinksWithCrawlee(url, options = {}) {
  const {
    maxLinks = 20,
    linkPattern = null
  } = options;

  const extractedLinks = [];
  let pageInfo = null;

  try {
    console.log(`Starting Crawlee link extraction from: ${url}`);

    const crawler = new PlaywrightCrawler({
      headless: true,
      launchContext: {
        launchOptions: {
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-web-security'
          ]
        }
      },
      browserPoolOptions: {
        useFingerprints: true,
        fingerprintOptions: {
          fingerprintGeneratorOptions: {
            browsers: ['chrome'],
            devices: ['desktop'],
            operatingSystems: ['windows']
          }
        }
      },
      requestHandler: async ({ page, request, log }) => {
        log.info(`Processing ${request.url}`);

        try {
          // Wait for content to load
          await page.waitForTimeout(2000);

          // Get page info
          pageInfo = await page.evaluate(() => {
            return {
              title: document.title,
              url: window.location.href,
              description: document.querySelector('meta[name="description"]')?.content || '',
              totalLinks: document.querySelectorAll('a[href]').length
            };
          });

          // Extract links
          const links = await page.evaluate(({ maxLinks, linkPattern }) => {
            const allLinks = Array.from(document.querySelectorAll('a[href]'));
            const extractedLinks = [];
            const seenUrls = new Set();

            for (const link of allLinks) {
              if (extractedLinks.length >= maxLinks) break;

              let href = link.href;
              
              // Skip invalid links
              if (!href || href.startsWith('javascript:') || href.startsWith('mailto:') || href.startsWith('tel:')) {
                continue;
              }

              // Convert relative URLs to absolute
              try {
                const url = new URL(href, window.location.href);
                href = url.href;
              } catch (e) {
                continue;
              }

              // Apply pattern filter if provided
              if (linkPattern && !href.includes(linkPattern)) {
                continue;
              }

              // Skip duplicates
              if (seenUrls.has(href)) {
                continue;
              }

              seenUrls.add(href);

              // Extract link information
              const linkText = link.textContent?.trim() || '';
              const title = link.title || '';
              const imageElement = link.querySelector('img');
              const image = imageElement ? imageElement.src : null;

              extractedLinks.push({
                url: href,
                text: linkText,
                title: title,
                image: image,
                domain: new URL(href).hostname
              });
            }

            return extractedLinks;
          }, { maxLinks, linkPattern });

          extractedLinks.push(...links);
          log.info(`Extracted ${links.length} links`);

        } catch (error) {
          log.error('Error during link extraction:', error);
          throw error;
        }
      },
      failedRequestHandler: async ({ request, log }) => {
        log.error(`Request ${request.url} failed`);
      },
      maxRequestsPerCrawl: 1,
      requestHandlerTimeoutSecs: 60
    });

    // Add the URL to crawl
    await crawler.addRequests([url]);

    // Run the crawler
    await crawler.run();

    console.log(`Crawlee extracted ${extractedLinks.length} links`);

    return {
      success: true,
      data: {
        pageInfo,
        links: extractedLinks,
        extractedCount: extractedLinks.length,
        totalLinksOnPage: pageInfo?.totalLinks || 0,
        extractedAt: new Date().toISOString()
      }
    };

  } catch (error) {
    console.error('Crawlee link extraction error:', error);
    return {
      success: false,
      error: 'Failed to extract links with Crawlee',
      message: error.message
    };
  }
}

// POST endpoint for link extraction
router.post('/', async (req, res) => {
  try {
    const { url, maxLinks = 20, linkPattern } = req.body;

    if (!url) {
      return res.status(400).json({
        success: false,
        error: 'URL is required'
      });
    }

    // Validate URL
    try {
      new URL(url);
    } catch (e) {
      return res.status(400).json({
        success: false,
        error: 'Invalid URL format'
      });
    }

    // Validate maxLinks
    const maxLinksNum = parseInt(maxLinks);
    if (isNaN(maxLinksNum) || maxLinksNum < 1 || maxLinksNum > 100) {
      return res.status(400).json({
        success: false,
        error: 'maxLinks must be a number between 1 and 100'
      });
    }

    console.log(`Crawlee link extraction request: ${url} (max: ${maxLinksNum})`);

    const result = await extractLinksWithCrawlee(url, {
      maxLinks: maxLinksNum,
      linkPattern: linkPattern?.trim() || null
    });

    res.json(result);

  } catch (error) {
    console.error('Crawlee route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error.message
    });
  }
});

module.exports = router;

import { useState } from 'react'
import ScrapingForm from './components/ScrapingForm'
import ResultDisplay from './components/ResultDisplay'
import './App.css'

function App() {
  const [scrapingResult, setScrapingResult] = useState(null)
  const [isLoading, setIsLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('scrape')
  const [abortController, setAbortController] = useState(null)

  const handleScrapingResult = (result) => {
    setScrapingResult(result)
    setIsLoading(false)
    setAbortController(null)
  }

  const handleScrapingStart = (controller) => {
    setIsLoading(true)
    setScrapingResult(null)
    setAbortController(controller)
  }

  const handleScrapingStop = () => {
    if (abortController) {
      abortController.abort()
      setAbortController(null)
    }
    setIsLoading(false)
    setScrapingResult({
      success: false,
      error: 'Scraping was cancelled by user',
      message: 'The scraping operation was stopped before completion.'
    })
  }

  const tabs = [
    { id: 'scrape', label: 'Single URL', path: '/scrape' },
    { id: 'crawl', label: 'Crawl', path: '/crawl', disabled: true },
    { id: 'map', label: 'Map', path: '/map', disabled: true },
    { id: 'search', label: 'Search', path: '/search', badge: 'NEW', disabled: true }
  ]

  return (
    <div className="app">
      <header className="app-header">
        <div className="header-content">
          <div className="header-title">
            <h1>Scrape Playground</h1>
            <p>Check out multiple libraries - all in one place</p>
          </div>
        </div>

        <nav className="tab-navigation">
          {tabs.map(tab => (
            <button
              key={tab.id}
              className={`tab ${activeTab === tab.id ? 'active' : ''} ${tab.disabled ? 'disabled' : ''}`}
              onClick={() => !tab.disabled && setActiveTab(tab.id)}
              disabled={tab.disabled}
            >
              {tab.label}
              {tab.path && <span className="tab-path">{tab.path}</span>}
              {tab.badge && <span className="tab-badge">{tab.badge}</span>}
            </button>
          ))}
        </nav>
      </header>

      <main className="app-main">
        {activeTab === 'scrape' && (
          <>
            <ScrapingForm
              onResult={handleScrapingResult}
              onStart={handleScrapingStart}
              onStop={handleScrapingStop}
              isLoading={isLoading}
            />

            {isLoading && (
              <div className="loading">
                <div className="spinner"></div>
                <p>Scraping in progress...</p>
                <p className="loading-hint">Click the Stop button to cancel</p>
              </div>
            )}

            {scrapingResult && (
              <ResultDisplay result={scrapingResult} />
            )}
          </>
        )}
      </main>
    </div>
  )
}

export default App

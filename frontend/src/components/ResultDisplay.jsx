import { useState, useEffect } from 'react'

// Image Gallery Component
const ImageGallery = ({ images, onImagesChange, showDeleteButtons = true }) => {
  const [imageErrors, setImageErrors] = useState({})
  const [imageLoading, setImageLoading] = useState({})

  const handleImageError = (index) => {
    setImageErrors(prev => ({ ...prev, [index]: true }))
    setImageLoading(prev => ({ ...prev, [index]: false }))
  }

  const handleImageLoad = (index) => {
    setImageLoading(prev => ({ ...prev, [index]: false }))
  }

  const handleImageLoadStart = (index) => {
    setImageLoading(prev => ({ ...prev, [index]: true }))
  }

  const deleteImage = (indexToDelete) => {
    const newImages = images.filter((_, index) => index !== indexToDelete)
    onImagesChange(newImages)
  }

  if (!images || images.length === 0) {
    return <div className="no-images">No images found</div>
  }

  return (
    <div className="image-gallery">
      <h4>🖼️ Images ({images.length})</h4>
      <div className="image-grid">
        {images.map((imageUrl, index) => (
          <div key={index} className="image-item">
            {showDeleteButtons && (
              <button
                className="delete-image-btn"
                onClick={() => deleteImage(index)}
                title="Delete image"
              >
                ✕
              </button>
            )}
            {imageLoading[index] && (
              <div className="image-loading">Loading...</div>
            )}
            {imageErrors[index] ? (
              <div className="image-error">
                <span>❌ Failed to load</span>
                <a href={imageUrl} target="_blank" rel="noopener noreferrer" className="image-url">
                  {imageUrl.length > 30 ? imageUrl.substring(0, 30) + '...' : imageUrl}
                </a>
              </div>
            ) : (
              <img
                src={imageUrl}
                alt={`Scraped image ${index + 1}`}
                onError={() => handleImageError(index)}
                onLoad={() => handleImageLoad(index)}
                onLoadStart={() => handleImageLoadStart(index)}
                loading="lazy"
              />
            )}
          </div>
        ))}
      </div>
    </div>
  )
}

// Editable Field Component
const EditableField = ({ label, value, onChange, type = 'text', isArray = false }) => {
  const [isEditing, setIsEditing] = useState(false)
  const [editValue, setEditValue] = useState(value)

  useEffect(() => {
    setEditValue(value)
  }, [value])

  const handleSave = () => {
    if (isArray) {
      // Handle array values (like splitting comma-separated strings)
      const arrayValue = typeof editValue === 'string'
        ? editValue.split(',').map(item => item.trim()).filter(item => item)
        : editValue
      onChange(arrayValue)
    } else {
      onChange(editValue)
    }
    setIsEditing(false)
  }

  const handleCancel = () => {
    setEditValue(value)
    setIsEditing(false)
  }

  const displayValue = isArray && Array.isArray(value) ? value.join(', ') : value

  return (
    <div className="editable-field">
      <label className="field-label">{label}:</label>
      {isEditing ? (
        <div className="edit-mode">
          {type === 'textarea' ? (
            <textarea
              value={isArray ? (Array.isArray(editValue) ? editValue.join(', ') : editValue) : editValue}
              onChange={(e) => setEditValue(e.target.value)}
              rows={3}
              className="edit-input"
            />
          ) : (
            <input
              type={type}
              value={isArray ? (Array.isArray(editValue) ? editValue.join(', ') : editValue) : editValue}
              onChange={(e) => setEditValue(e.target.value)}
              className="edit-input"
            />
          )}
          <div className="edit-actions">
            <button onClick={handleSave} className="save-btn">✓</button>
            <button onClick={handleCancel} className="cancel-btn">✕</button>
          </div>
        </div>
      ) : (
        <div className="view-mode" onClick={() => setIsEditing(true)}>
          <span className="field-value">
            {displayValue || <em>No value</em>}
          </span>
          <span className="edit-hint">✏️</span>
        </div>
      )}
    </div>
  )
}

// Simple JSON Display Component
const JsonDisplay = ({ data, level = 0 }) => {
  const [collapsed, setCollapsed] = useState({})

  const toggleCollapse = (key) => {
    setCollapsed(prev => ({ ...prev, [key]: !prev[key] }))
  }

  const renderValue = (value, key, currentLevel) => {
    if (value === null) return <span className="json-null">null</span>
    if (typeof value === 'boolean') return <span className="json-boolean">{String(value)}</span>
    if (typeof value === 'number') return <span className="json-number">{value}</span>
    if (typeof value === 'string') {
      if (value.startsWith('http')) {
        return <a href={value} target="_blank" rel="noopener noreferrer" className="json-link">{value}</a>
      }
      return <span className="json-string">"{value}"</span>
    }
    if (Array.isArray(value)) {
      const isCollapsed = collapsed[key]
      return (
        <div className="json-array">
          <span
            className="json-toggle"
            onClick={() => toggleCollapse(key)}
          >
            {isCollapsed ? '▶' : '▼'} [{value.length} items]
          </span>
          {!isCollapsed && (
            <div className="json-content" style={{ marginLeft: '20px' }}>
              {value.map((item, index) => (
                <div key={index} className="json-item">
                  <span className="json-key">{index}:</span> {renderValue(item, `${key}-${index}`, currentLevel + 1)}
                </div>
              ))}
            </div>
          )}
        </div>
      )
    }
    if (typeof value === 'object') {
      const isCollapsed = collapsed[key]
      const keys = Object.keys(value)
      return (
        <div className="json-object">
          <span
            className="json-toggle"
            onClick={() => toggleCollapse(key)}
          >
            {isCollapsed ? '▶' : '▼'} {`{${keys.length} keys}`}
          </span>
          {!isCollapsed && (
            <div className="json-content" style={{ marginLeft: '20px' }}>
              {keys.map(objKey => (
                <div key={objKey} className="json-item">
                  <span className="json-key">"{objKey}":</span> {renderValue(value[objKey], `${key}-${objKey}`, currentLevel + 1)}
                </div>
              ))}
            </div>
          )}
        </div>
      )
    }
    return String(value)
  }

  return (
    <div className="json-display">
      {Object.entries(data).map(([key, value]) => (
        <div key={key} className="json-item">
          <span className="json-key">"{key}":</span> {renderValue(value, key, level)}
        </div>
      ))}
    </div>
  )
}

const ResultDisplay = ({ result }) => {
  const [viewMode, setViewMode] = useState('formatted')
  const [editableData, setEditableData] = useState(null)

  // Initialize editable data when result changes
  useEffect(() => {
    if (result && result.data) {
      setEditableData({ ...result.data })
    }
  }, [result])

  // Update a specific field in the editable data
  const updateField = (fieldName, newValue) => {
    setEditableData(prev => ({
      ...prev,
      [fieldName]: newValue
    }))
  }

  // Get the current result with editable data
  const getCurrentResult = () => {
    if (!result || !editableData) return result
    return {
      ...result,
      data: editableData
    }
  }

  const downloadJson = () => {
    const currentResult = getCurrentResult()
    const dataStr = JSON.stringify(currentResult, null, 2)
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)

    const exportFileDefaultName = `scraped-data-${new Date().toISOString().split('T')[0]}.json`

    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
  }

  const copyToClipboard = () => {
    const currentResult = getCurrentResult()
    navigator.clipboard.writeText(JSON.stringify(currentResult, null, 2))
      .then(() => alert('JSON copied to clipboard!'))
      .catch(err => console.error('Failed to copy: ', err))
  }

  if (!result) return null

  return (
    <div className="result-display">
      <div className="result-header">
        <h2>Scraping Results</h2>
        <div className="result-actions">
          <div className="view-toggle">
            <button 
              className={viewMode === 'formatted' ? 'active' : ''}
              onClick={() => setViewMode('formatted')}
            >
              Formatted
            </button>
            <button 
              className={viewMode === 'raw' ? 'active' : ''}
              onClick={() => setViewMode('raw')}
            >
              Raw JSON
            </button>
          </div>
          <button onClick={copyToClipboard} className="action-btn">
            📋 Copy
          </button>
          <button onClick={downloadJson} className="action-btn">
            💾 Download
          </button>
        </div>
      </div>

      <div className="result-content">
        {result.success === false ? (
          <div className="error-result">
            <h3>❌ Scraping Failed</h3>
            <p><strong>Error:</strong> {result.error}</p>
            {result.message && <p><strong>Details:</strong> {result.message}</p>}
          </div>
        ) : (
          <>
            <div className="result-meta">
              <div className="meta-item">
                <strong>URL:</strong> {result.url}
              </div>
              <div className="meta-item">
                <strong>Scraper:</strong> {result.scraper}
              </div>
              <div className="meta-item">
                <strong>Timestamp:</strong> {new Date(result.timestamp).toLocaleString()}
              </div>
            </div>

            {viewMode === 'formatted' ? (
              <div className="formatted-result">
                {editableData && (
                  <div className="editable-results">
                    <div className="results-layout">
                      <div className="main-content">
                        <h3>📝 Editable Results</h3>
                        <div className="editable-fields">
                          {Object.entries(editableData).map(([key, value]) => {
                            if (key === 'images') {
                              return null // Handle images separately
                            }

                            const isArray = Array.isArray(value)
                            const fieldType = key.includes('description') || key.includes('content') ? 'textarea' : 'text'

                            return (
                              <EditableField
                                key={key}
                                label={key.charAt(0).toUpperCase() + key.slice(1)}
                                value={value}
                                onChange={(newValue) => updateField(key, newValue)}
                                type={fieldType}
                                isArray={isArray}
                              />
                            )
                          })}
                        </div>
                      </div>

                      {editableData.images && editableData.images.length > 0 && (
                        <div className="images-sidebar">
                          <ImageGallery
                            images={editableData.images}
                            onImagesChange={(newImages) => updateField('images', newImages)}
                            showDeleteButtons={true}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <pre className="raw-json">
                {JSON.stringify(getCurrentResult(), null, 2)}
              </pre>
            )}

            {result.data && Object.keys(result.data).length > 0 && (
              <div className="data-summary">
                <h3>📊 Data Summary</h3>
                <ul>
                  {Object.entries(result.data).map(([key, value]) => (
                    <li key={key}>
                      <strong>{key}:</strong> 
                      {Array.isArray(value) ? (
                        <span> {value.length} items</span>
                      ) : typeof value === 'string' ? (
                        <span> "{value.length > 50 ? value.substring(0, 50) + '...' : value}"</span>
                      ) : (
                        <span> {String(value)}</span>
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}

export default ResultDisplay

import { useState } from 'react'
import axios from 'axios'

const SearchForm = ({ onResult, onStart, onStop, isLoading }) => {
  const [url, setUrl] = useState('')
  const [scraper, setScraper] = useState('stealth-playwright')
  const [maxLinks, setMaxLinks] = useState(20)
  const [linkPattern, setLinkPattern] = useState('')
  const [showAdvanced, setShowAdvanced] = useState(false)

  const scraperOptions = [
    {
      id: 'stealth-playwright',
      label: 'Stealth Playwright',
      description: 'Best for modern e-commerce sites',
      details: 'Uses stealth techniques to bypass bot detection. Ideal for sites like Decathlon, Amazon, etc.'
    },
    {
      id: 'crawlee',
      label: 'Crawlee',
      description: 'JavaScript-rendered content',
      details: 'Modern web scraping with Playwright integration. Perfect for dynamic content.'
    },
    {
      id: 'cheerio',
      label: 'Cheerio',
      description: 'Fast HTML parsing',
      details: 'Server-side jQuery for Node.js. Very fast for static HTML content.'
    }
  ]

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!url) {
      alert('Please enter a URL')
      return
    }

    // Create AbortController for this request
    const controller = new AbortController()
    onStart(controller)

    try {
      const endpoint = `http://localhost:3001/api/search/${scraper}`

      const payload = {
        url,
        maxLinks: parseInt(maxLinks),
        linkPattern: linkPattern.trim() || null
      }

      const response = await axios.post(endpoint, payload, {
        signal: controller.signal,
        timeout: 120000 // 2 minute timeout for link extraction
      })
      onResult(response.data)
    } catch (error) {
      console.error('Link extraction error:', error)
      
      // Check if the error was due to cancellation
      if (error.name === 'CanceledError' || error.code === 'ERR_CANCELED') {
        return
      }
      
      onResult({
        success: false,
        error: error.response?.data?.error || 'Failed to extract links',
        message: error.response?.data?.message || error.message
      })
    }
  }

  return (
    <div className="scraping-form">
      <div className="url-section">
        <label className="url-label">Category/Listing Page URL</label>
        <div className="url-input-container">
          <input
            type="url"
            className="url-input"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            placeholder="https://www.decathlon.de/alle-sportarten-a-z/fahrrad-welt/e-mountainbikes"
            disabled={isLoading}
          />
          <div className="url-actions">
            <button
              type="button"
              className="btn-primary"
              onClick={handleSubmit}
              disabled={isLoading || !url.trim()}
            >
              <span className="btn-icon">🔍</span>
              Extract Links
            </button>
            <button 
              type="button" 
              className="btn-secondary btn-stop" 
              disabled={!isLoading}
              onClick={onStop}
            >
              <span className="btn-icon">⏹️</span>
              Stop
            </button>
          </div>
        </div>
      </div>

      <div className="options-section">
        <button
          type="button"
          className="options-toggle"
          onClick={() => setShowAdvanced(!showAdvanced)}
        >
          Options {showAdvanced ? '▲' : '▼'}
        </button>
      </div>

      {showAdvanced && (
        <div className="advanced-options">
          <form onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="search-url">Category/Listing Page URL:</label>
              <input
                type="url"
                id="search-url"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                placeholder="https://example.com/category/products"
                required
                disabled={isLoading}
              />
              <small>Enter a URL that contains multiple product/listing links</small>
            </div>

            <div className="form-group">
              <label>Scraper Engine:</label>
              <div className="scraper-options">
                {scraperOptions.map(option => (
                  <div key={option.id} className="radio-option">
                    <input
                      type="radio"
                      id={option.id}
                      name="scraper"
                      value={option.id}
                      checked={scraper === option.id}
                      onChange={(e) => setScraper(e.target.value)}
                      disabled={isLoading}
                    />
                    <div className="radio-content">
                      <div className="radio-header">
                        <strong>{option.label}</strong>
                        <div className="radio-description">{option.description}</div>
                      </div>
                      <div className="radio-details">{option.details}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="maxLinks">Maximum Links to Extract:</label>
              <input
                type="number"
                id="maxLinks"
                value={maxLinks}
                onChange={(e) => setMaxLinks(e.target.value)}
                min="1"
                max="100"
                disabled={isLoading}
              />
              <small>Limit the number of product links to extract (1-100)</small>
            </div>

            <div className="form-group">
              <label htmlFor="linkPattern">Link Pattern Filter (optional):</label>
              <input
                type="text"
                id="linkPattern"
                value={linkPattern}
                onChange={(e) => setLinkPattern(e.target.value)}
                placeholder="e.g., /product/, /p/, /item/"
                disabled={isLoading}
              />
              <small>Only extract links containing this pattern (leave empty for all links)</small>
            </div>

            <button type="submit" disabled={isLoading} className="submit-btn">
              {isLoading ? 'Extracting Links...' : 'Extract Product Links'}
            </button>
          </form>
        </div>
      )}

      {/* Status Section */}
      <div className="status-section">
        <div className="status-info">
          <span className="status-text">
            {isLoading ? (
              <>
                <span className="status-indicator streaming">🟠</span>
                Extracting product links...
              </>
            ) : (
              <>
                <span className="status-indicator">🔍</span>
                Ready to extract links from category pages
              </>
            )}
          </span>
          <div className="status-actions">
            <button className="status-btn" disabled={!isLoading}>
              📥 Download Results
            </button>
            <button className="status-btn error" disabled>
              ⚠️ Report Issue
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SearchForm
